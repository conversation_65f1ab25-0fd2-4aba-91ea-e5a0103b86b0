<?php

namespace App\Http\Controllers;

use App\Http\Requests\ProfileUpdateRequest;
use App\Services\UserChangeLogService;
use Illuminate\Http\RedirectResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Redirect;
use Illuminate\Support\Facades\Storage;
use Illuminate\View\View;

class ProfileController extends Controller
{
    protected UserChangeLogService $changeLogService;

    public function __construct(UserChangeLogService $changeLogService)
    {
        $this->changeLogService = $changeLogService;
    }

    /**
     * Display the user's profile form.
     */
    public function edit(Request $request): View
    {
        $user = $request->user();

        // Optimize database queries with eager loading
        $user->load([
            'galleryImages' => function ($query) {
                $query->active()->ordered();
            },
            'subscriptions' => function ($query) {
                $query->orderBy('created_at', 'desc')->limit(3);
            }
        ]);

        // Get recent changes (optimized)
        $recentChanges = $this->changeLogService->getFormattedChangeLogs($user, 5);

        // Get wallet information (cached) with error handling
        try {
            $wallet = $user->getWallet();
            $totalEarnings = $wallet->total_earned ?? 0;
            $currentBalance = $wallet->balance ?? 0;
        } catch (\Exception $e) {
            \Log::error('Wallet error in profile: ' . $e->getMessage());
            $wallet = new \App\Models\UserWallet(['balance' => 0, 'total_earned' => 0, 'total_withdrawn' => 0]);
            $totalEarnings = 0;
            $currentBalance = 0;
        }

        // Get total spending (as client) - optimized query with error handling
        try {
            $totalSpending = $user->clientBookings()
                ->where('payment_status', 'paid')
                ->sum('total_amount') ?? 0;
        } catch (\Exception $e) {
            $totalSpending = 0;
        }

        // Get recent transactions with optimized eager loading and error handling
        try {
            $recentTransactions = $user->walletTransactions()
                ->with(['booking:id,client_id,provider_id,total_amount,start_time', 'booking.client:id,name', 'booking.provider:id,name'])
                ->orderBy('created_at', 'desc')
                ->limit(5)
                ->get();
        } catch (\Exception $e) {
            $recentTransactions = collect();
        }

        // Update subscription status with error handling
        try {
            $user->updateSubscriptionStatus();
        } catch (\Exception $e) {
            \Log::error('Subscription status update error in profile: ' . $e->getMessage());
        }

        return view('profile.edit', [
            'user' => $user,
            'recentChanges' => $recentChanges,
            'wallet' => $wallet,
            'totalEarnings' => $totalEarnings,
            'currentBalance' => $currentBalance,
            'totalSpending' => $totalSpending,
            'recentTransactions' => $recentTransactions,
        ]);
    }

    /**
     * Update the user's profile information.
     */
    public function update(ProfileUpdateRequest $request): RedirectResponse
    {
        $user = $request->user();
        $validatedData = $request->validated();

        // Store original data for change logging
        $originalData = $user->getOriginal();

        // Handle profile picture upload
        if ($request->hasFile('profile_picture')) {
            // Delete old profile picture if exists
            if ($user->profile_picture) {
                Storage::disk('public')->delete($user->profile_picture);
            }

            // Store new profile picture
            $path = $request->file('profile_picture')->store('profile-pictures', 'public');
            $validatedData['profile_picture'] = $path;
        }

        $user->fill($validatedData);

        if ($user->isDirty('email')) {
            $user->email_verified_at = null;
        }

        // Save the user
        $user->save();

        // Log the changes
        $this->changeLogService->logProfileChanges($user, $originalData, $user->toArray(), $request);

        // Get the current tab from request to redirect back to the same tab
        $currentTab = $request->input('current_tab', 'personal');
        return Redirect::route('profile.edit', ['tab' => $currentTab])->with('status', 'profile-updated');
    }

    /**
     * Update the user's privacy settings.
     */
    public function updatePrivacySettings(Request $request): RedirectResponse
    {
        $user = $request->user();

        // Validate privacy settings
        $validatedData = $request->validate([
            'is_public_profile' => 'boolean',
            'show_date_of_birth' => 'boolean',
            'hide_dob_year' => 'boolean',
            'show_interests_hobbies' => 'boolean',
            'show_expectations' => 'boolean',
            'show_gallery_images' => 'boolean',
            'is_time_spending_enabled' => 'boolean',
        ]);

        // Convert checkbox values (checkboxes send '1' when checked, nothing when unchecked)
        $privacySettings = [
            'is_public_profile' => $request->has('is_public_profile'),
            'show_contact_number' => false, // Always keep disabled
            'show_date_of_birth' => $request->has('show_date_of_birth'),
            'hide_dob_year' => $request->has('hide_dob_year'),
            'show_interests_hobbies' => $request->has('show_interests_hobbies'),
            'show_expectations' => $request->has('show_expectations'),
            'show_gallery_images' => $request->has('show_gallery_images'),
        ];

        // Handle gallery settings if feature is enabled
        if (\App\Models\Feature::isEnabled('gallery')) {
            // Gallery setting is already handled in the main privacy settings array
            // No additional logic needed here as it's just a simple toggle
        } else {
            // If gallery feature is disabled by admin, force gallery setting to false
            $privacySettings['show_gallery_images'] = false;
        }

        // Handle time spending settings if feature is enabled
        if (\App\Models\Feature::isEnabled('time_spending')) {
            $isTimeSpendingEnabled = $request->has('is_time_spending_enabled');

            // If enabling time spending, automatically enable required privacy settings
            if ($isTimeSpendingEnabled) {
                $privacySettings['is_public_profile'] = true;
                // Only enable gallery images if gallery feature is enabled by admin
                if (\App\Models\Feature::isEnabled('gallery')) {
                    $privacySettings['show_gallery_images'] = true;
                }

                // If subscription model is disabled, activate Time Spending immediately
                if (!\App\Models\Feature::isSubscriptionModelEnabled()) {
                    $privacySettings['has_active_time_spending_subscription'] = true;
                    $privacySettings['time_spending_subscription_expires_at'] = now()->addYears(10); // Set far future date
                }
            }

            $privacySettings['is_time_spending_enabled'] = $isTimeSpendingEnabled;
        } else {
            // If time spending feature is disabled by admin, force time spending setting to false
            $privacySettings['is_time_spending_enabled'] = false;
        }

        // Handle couple activity settings if feature is enabled
        if (\App\Models\Feature::isEnabled('partner_swapping')) {
            $isCoupleActivityEnabled = $request->has('is_couple_activity_enabled');

            // If enabling couple activity, automatically enable required privacy settings
            if ($isCoupleActivityEnabled) {
                $privacySettings['is_public_profile'] = true;
            }

            $privacySettings['is_couple_activity_enabled'] = $isCoupleActivityEnabled;

            // Reset couple activity status to available if enabling
            if ($isCoupleActivityEnabled) {
                $privacySettings['couple_activity_status'] = 'available';
            }
        } else {
            // If couple activity feature is disabled by admin, force couple activity setting to false
            $privacySettings['is_couple_activity_enabled'] = false;
            $privacySettings['couple_activity_status'] = 'inactive';
        }

        // Handle sugar partner settings if feature is enabled
        if (\App\Models\Feature::isEnabled('sugar_partner')) {
            $isSugarPartnerEnabled = $request->has('interested_in_sugar_partner');

            // If enabling sugar partner, automatically enable required privacy settings
            if ($isSugarPartnerEnabled) {
                $privacySettings['is_public_profile'] = true;
            }

            $privacySettings['interested_in_sugar_partner'] = $isSugarPartnerEnabled;
        } else {
            // If sugar partner feature is disabled by admin, force sugar partner setting to false
            $privacySettings['interested_in_sugar_partner'] = false;
        }

        // Store original data for change logging
        $originalData = $user->getOriginal();

        // Update privacy settings
        $user->fill($privacySettings);
        $user->save();

        // Log the changes
        $this->changeLogService->logProfileChanges($user, $originalData, $user->toArray(), $request);

        // Get the current tab from request to redirect back to the same tab
        $currentTab = $request->input('current_tab', 'settings');
        return Redirect::route('profile.edit', ['tab' => $currentTab])->with('status', 'privacy-settings-updated');
    }

    /**
     * Update the user's time spending settings.
     */
    public function updateTimeSpending(Request $request): RedirectResponse
    {
        $user = $request->user();

        // Check if time spending feature is enabled by admin
        if (!\App\Models\Feature::isEnabled('time_spending')) {
            return Redirect::route('profile.edit')
                ->with('error', 'Time Spending feature is not available.');
        }

        // Debug: Check what data is being sent
        \Log::info('Time Spending Form Data:', $request->all());

        // Validate time spending data
        $validatedData = $request->validate([
            'hourly_rate' => 'required|numeric|min:0|max:999999.99',
            'service_location' => 'required|string|max:255',
            'location_selected' => 'nullable|in:0,1', // Make it nullable for now to debug
            'availability_schedule' => 'nullable|array',
            'availability_schedule.*.start_time' => 'nullable|string',
            'availability_schedule.*.end_time' => 'nullable|string',
            'availability_schedule.*.is_holiday' => 'nullable',
        ], [
            'service_location.required' => 'Please select a location from the dropdown suggestions.',
        ]);

        // Additional validation for location selection
        if (!isset($validatedData['location_selected']) || $validatedData['location_selected'] !== '1') {
            return back()->withErrors([
                'service_location' => 'Please select a location from the dropdown suggestions. Manual typing is not allowed.'
            ])->withInput();
        }

        // Additional validation for time format and logic
        if (isset($validatedData['availability_schedule'])) {
            $days = ['sunday', 'monday', 'tuesday', 'wednesday', 'thursday', 'friday', 'saturday'];

            foreach ($days as $day) {
                if (isset($validatedData['availability_schedule'][$day])) {
                    $daySchedule = $validatedData['availability_schedule'][$day];

                    // Skip validation if it's a holiday
                    if (isset($daySchedule['is_holiday']) && $daySchedule['is_holiday'] == '1') {
                        continue;
                    }

                    // Validate time format and logic
                    if (isset($daySchedule['start_time']) && isset($daySchedule['end_time'])) {
                        $startTime = $daySchedule['start_time'];
                        $endTime = $daySchedule['end_time'];

                        // Validate time format (HH:MM)
                        if (!preg_match('/^([0-1]?[0-9]|2[0-3]):[0-5][0-9]$/', $startTime) ||
                            !preg_match('/^([0-1]?[0-9]|2[0-3]):[0-5][0-9]$/', $endTime)) {
                            return back()->withErrors(['availability_schedule' => "Invalid time format for " . ucfirst($day) . ". Please use HH:MM format."])->withInput();
                        }

                        // Validate 15-minute increments
                        $startMinutes = (int)explode(':', $startTime)[1];
                        $endMinutes = (int)explode(':', $endTime)[1];

                        if ($startMinutes % 15 !== 0 || $endMinutes % 15 !== 0) {
                            return back()->withErrors(['availability_schedule' => "Time for " . ucfirst($day) . " must be in 15-minute increments (e.g., 09:00, 09:15, 09:30, 09:45)."])->withInput();
                        }

                        // Validate that end time is after start time
                        $startDateTime = \DateTime::createFromFormat('H:i', $startTime);
                        $endDateTime = \DateTime::createFromFormat('H:i', $endTime);

                        if ($startDateTime >= $endDateTime) {
                            return back()->withErrors(['availability_schedule' => "End time must be after start time for " . ucfirst($day) . "."])->withInput();
                        }

                        // Validate minimum duration (at least 30 minutes)
                        $diffInMinutes = ($endDateTime->getTimestamp() - $startDateTime->getTimestamp()) / 60;
                        if ($diffInMinutes < 30) {
                            return back()->withErrors(['availability_schedule' => "Minimum availability duration for " . ucfirst($day) . " should be 30 minutes."])->withInput();
                        }
                    }
                }
            }
        }

        // Store original data for change logging
        $originalData = $user->getOriginal();

        // Process availability schedule
        $availabilitySchedule = [];
        if (isset($validatedData['availability_schedule'])) {
            foreach ($validatedData['availability_schedule'] as $day => $schedule) {
                $isHoliday = isset($schedule['is_holiday']) && $schedule['is_holiday'] == '1';
                $availabilitySchedule[$day] = [
                    'start_time' => $isHoliday ? '09:00' : ($schedule['start_time'] ?? '09:00'),
                    'end_time' => $isHoliday ? '17:00' : ($schedule['end_time'] ?? '17:00'),
                    'is_holiday' => $isHoliday
                ];
            }
        }

        // Update time spending settings
        $user->hourly_rate = $validatedData['hourly_rate'];
        $user->service_location = $validatedData['service_location'];
        $user->currency = 'INR'; // Force INR only
        $user->availability_schedule = $availabilitySchedule;
        $user->save();

        // Log the changes
        $this->changeLogService->logProfileChanges($user, $originalData, $user->toArray(), $request);

        // Get the current tab from request to redirect back to the same tab
        $currentTab = $request->input('current_tab', 'time-spending');
        return Redirect::route('profile.edit', ['tab' => $currentTab])->with('status', 'time-spending-updated');
    }

    /**
     * Update the user's couple activity settings.
     */
    public function updateCoupleActivity(Request $request): RedirectResponse
    {
        $user = $request->user();

        // Store original data for change logging
        $originalData = $user->toArray();

        // Validate the request
        $request->validate([
            'is_couple_activity_enabled' => 'nullable|boolean',
        ]);

        // Update couple activity settings
        $user->update([
            'is_couple_activity_enabled' => $request->boolean('is_couple_activity_enabled'),
        ]);

        // If couple activity is enabled, automatically make profile public
        if ($request->boolean('is_couple_activity_enabled')) {
            $user->update([
                'is_public_profile' => true,
            ]);
        }

        // Log the changes
        $this->changeLogService->logProfileChanges($user, $originalData, $user->toArray(), $request);

        return Redirect::route('profile.edit', ['tab' => $request->current_tab])->with('status', 'couple-activity-updated');
    }

    /**
     * Update the user's sugar partner settings.
     */
    public function updateSugarPartner(Request $request): RedirectResponse
    {
        $user = $request->user();

        // Validate the request
        $validatedData = $request->validate([
            'sugar_partner_types' => 'nullable|array',
            'sugar_partner_types.*' => 'string|in:sugar_daddy,sugar_mommy,sugar_companion_female,sugar_companion_male',
            'sugar_partner_bio' => 'nullable|string|max:1000',
            'sugar_partner_expectations' => 'nullable|string|max:1000',
            'current_tab' => 'string'
        ]);

        // Store original data for change logging
        $originalData = $user->getOriginal();

        // Update sugar partner settings
        $user->sugar_partner_types = $validatedData['sugar_partner_types'] ?? [];
        $user->sugar_partner_bio = $validatedData['sugar_partner_bio'] ?? null;
        $user->sugar_partner_expectations = $validatedData['sugar_partner_expectations'] ?? null;
        $user->save();

        // Log the changes
        $this->changeLogService->logProfileChanges($user, $originalData, $user->toArray(), $request);

        return Redirect::route('profile.edit', ['tab' => $request->current_tab])->with('status', 'sugar-partner-updated');
    }

    /**
     * Delete the user's account.
     */
    public function destroy(Request $request): RedirectResponse
    {
        // No password validation needed for Google OAuth users
        $user = $request->user();

        // Delete profile picture if exists
        if ($user->profile_picture) {
            Storage::disk('public')->delete($user->profile_picture);
        }

        Auth::logout();

        $user->delete();

        $request->session()->invalidate();
        $request->session()->regenerateToken();

        return Redirect::to('/')->with('status', 'account-deleted');
    }
}
