@extends('layouts.admin')

@section('page-title', 'Sugar Partner Profile')

@section('page-description', 'Detailed profile view for ' . $user->name)

@section('breadcrumbs')
    <li class="breadcrumb-item">
        <a href="{{ route('admin.sugar-partners.index') }}" class="text-decoration-none">Sugar Partners</a>
    </li>
    <li class="breadcrumb-item">
        <span class="text-muted">{{ $user->name }}</span>
    </li>
@endsection

@section('header-actions')
    <div class="d-flex gap-2">
        <a href="{{ route('admin.sugar-partners.index') }}" class="btn btn-outline-secondary">
            <i class="bi bi-arrow-left me-2"></i>Back to List
        </a>
        @if($user->is_suspended ?? false)
            <button type="button" class="btn btn-success btn-activate-user" data-user-id="{{ $user->id }}">
                <i class="bi bi-check-circle me-2"></i>Activate User
            </button>
        @else
            <button type="button" class="btn btn-warning btn-suspend-user" data-user-id="{{ $user->id }}">
                <i class="bi bi-pause-circle me-2"></i>Suspend User
            </button>
        @endif
    </div>
@endsection

@section('content')
<div class="row">
    <!-- Profile Overview -->
    <div class="col-lg-4">
        <div class="card border-0 shadow-sm mb-4">
            <div class="card-body text-center">
                @if($user->profile_picture)
                    <img class="rounded-circle mb-3" src="{{ asset('storage/' . $user->profile_picture) }}" 
                         alt="{{ $user->name }}" style="width: 120px; height: 120px; object-fit: cover;">
                @else
                    <div class="rounded-circle mx-auto mb-3 d-flex align-items-center justify-content-center"
                         style="width: 120px; height: 120px; background: linear-gradient(135deg, #e11d48 0%, #f43f5e 100%);">
                        <span class="text-white fw-bold fs-1">{{ substr($user->name, 0, 1) }}</span>
                    </div>
                @endif
                
                <h4 class="fw-bold mb-1">{{ $user->name }}</h4>
                <p class="text-muted mb-2">ID: {{ $user->id }}</p>
                
                @if($user->is_suspended ?? false)
                    <span class="badge bg-danger-subtle text-danger mb-3">
                        <i class="bi bi-pause-circle me-1"></i>Suspended
                    </span>
                @else
                    <span class="badge bg-success-subtle text-success mb-3">
                        <i class="bi bi-check-circle me-1"></i>Active
                    </span>
                @endif

                <div class="d-flex justify-content-center gap-2 mb-3">
                    @if($user->email_verified_at)
                        <span class="badge bg-success-subtle text-success">
                            <i class="bi bi-check-circle me-1"></i>Email Verified
                        </span>
                    @else
                        <span class="badge bg-warning-subtle text-warning">
                            <i class="bi bi-exclamation-circle me-1"></i>Email Unverified
                        </span>
                    @endif
                </div>

                <div class="text-start">
                    <small class="text-muted">Member since</small>
                    <div class="fw-medium">{{ $user->created_at->format('M d, Y') }}</div>
                </div>
            </div>
        </div>

        <!-- Sugar Partner Types -->
        <div class="card border-0 shadow-sm mb-4">
            <div class="card-header bg-white border-bottom">
                <h6 class="card-title mb-0 fw-bold">
                    <i class="bi bi-heart me-2 text-danger"></i>Sugar Partner Types
                </h6>
            </div>
            <div class="card-body">
                @if($user->sugar_partner_types && count($user->sugar_partner_types) > 0)
                    <div class="d-flex flex-wrap gap-2">
                        @foreach($user->getSugarPartnerTypesForAdmin() as $type)
                            <span class="badge bg-danger-subtle text-danger">
                                <i class="bi bi-heart me-1"></i>{{ $type }}
                            </span>
                        @endforeach
                    </div>
                @else
                    <p class="text-muted mb-0">No sugar partner types selected</p>
                @endif
            </div>
        </div>
    </div>

    <!-- Detailed Information -->
    <div class="col-lg-8">
        <!-- Basic Information -->
        <div class="card border-0 shadow-sm mb-4">
            <div class="card-header bg-white border-bottom">
                <h6 class="card-title mb-0 fw-bold">
                    <i class="bi bi-person me-2 text-primary"></i>Basic Information
                </h6>
            </div>
            <div class="card-body">
                <div class="row g-3">
                    <div class="col-md-6">
                        <label class="form-label fw-medium text-muted">Email Address</label>
                        <div class="d-flex align-items-center">
                            <span>{{ $user->email }}</span>
                            <i class="bi {{ $user->email_verified_at ? 'bi-check-circle text-success' : 'bi-x-circle text-danger' }} ms-2"></i>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <label class="form-label fw-medium text-muted">Phone Number</label>
                        <div>{{ $user->contact_number ?? 'Not provided' }}</div>
                    </div>
                    <div class="col-md-6">
                        <label class="form-label fw-medium text-muted">Gender</label>
                        <div>
                            @if($user->gender === 'male')
                                <span class="badge bg-info-subtle text-info">
                                    <i class="bi bi-person me-1"></i>Male
                                </span>
                            @elseif($user->gender === 'female')
                                <span class="badge bg-warning-subtle text-warning">
                                    <i class="bi bi-person-dress me-1"></i>Female
                                </span>
                            @else
                                <span class="text-muted">Not specified</span>
                            @endif
                        </div>
                    </div>
                    <div class="col-md-6">
                        <label class="form-label fw-medium text-muted">Date of Birth</label>
                        <div>{{ $user->date_of_birth ? $user->date_of_birth->format('M d, Y') : 'Not provided' }}</div>
                    </div>
                    <div class="col-12">
                        <label class="form-label fw-medium text-muted">Interests</label>
                        <div>
                            @if($user->interests)
                                @foreach(explode(',', $user->interests) as $interest)
                                    <span class="badge bg-secondary-subtle text-secondary me-1">{{ trim($interest) }}</span>
                                @endforeach
                            @else
                                <span class="text-muted">Not specified</span>
                            @endif
                        </div>
                    </div>
                    <div class="col-12">
                        <label class="form-label fw-medium text-muted">Expectations</label>
                        <div>{{ $user->expectation ?? 'Not provided' }}</div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Sugar Partner Information -->
        <div class="card border-0 shadow-sm mb-4">
            <div class="card-header bg-white border-bottom">
                <h6 class="card-title mb-0 fw-bold">
                    <i class="bi bi-heart me-2 text-danger"></i>Sugar Partner Information
                </h6>
            </div>
            <div class="card-body">
                <div class="row g-3">
                    <div class="col-12">
                        <label class="form-label fw-medium text-muted">Sugar Partner Bio</label>
                        <div class="bg-light p-3 rounded">
                            {{ $user->sugar_partner_bio ?? 'No bio provided' }}
                        </div>
                    </div>
                    <div class="col-12">
                        <label class="form-label fw-medium text-muted">Sugar Partner Expectations</label>
                        <div class="bg-light p-3 rounded">
                            {{ $user->sugar_partner_expectations ?? 'No expectations provided' }}
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Gallery Images -->
        @if($user->galleryImages->count() > 0)
        <div class="card border-0 shadow-sm mb-4">
            <div class="card-header bg-white border-bottom">
                <h6 class="card-title mb-0 fw-bold">
                    <i class="bi bi-images me-2 text-success"></i>Gallery Images ({{ $user->galleryImages->count() }})
                </h6>
            </div>
            <div class="card-body">
                <div class="row g-3">
                    @foreach($user->galleryImages as $image)
                        <div class="col-md-4">
                            <div class="position-relative">
                                <img src="{{ asset('storage/' . $image->image_path) }}" 
                                     alt="Gallery Image" 
                                     class="img-fluid rounded shadow-sm"
                                     style="width: 100%; height: 200px; object-fit: cover; cursor: pointer;"
                                     onclick="showImageModal('{{ asset('storage/' . $image->image_path) }}')">
                                <div class="position-absolute top-0 end-0 m-2">
                                    <span class="badge bg-dark bg-opacity-75">{{ $loop->iteration }}</span>
                                </div>
                            </div>
                        </div>
                    @endforeach
                </div>
            </div>
        </div>
        @endif

        <!-- Account Status & Settings -->
        <div class="card border-0 shadow-sm">
            <div class="card-header bg-white border-bottom">
                <h6 class="card-title mb-0 fw-bold">
                    <i class="bi bi-gear me-2 text-secondary"></i>Account Status & Settings
                </h6>
            </div>
            <div class="card-body">
                <div class="row g-3">
                    <div class="col-md-6">
                        <label class="form-label fw-medium text-muted">Account Status</label>
                        <div>
                            @if($user->is_suspended ?? false)
                                <span class="badge bg-danger-subtle text-danger">
                                    <i class="bi bi-pause-circle me-1"></i>Suspended
                                </span>
                            @else
                                <span class="badge bg-success-subtle text-success">
                                    <i class="bi bi-check-circle me-1"></i>Active
                                </span>
                            @endif
                        </div>
                    </div>
                    <div class="col-md-6">
                        <label class="form-label fw-medium text-muted">Payment Status</label>
                        <div>
                            @if($user->paid_at)
                                <span class="badge bg-success-subtle text-success">
                                    <i class="bi bi-check-circle me-1"></i>Paid
                                </span>
                                <small class="text-muted d-block">{{ $user->paid_at->format('M d, Y') }}</small>
                            @else
                                <span class="badge bg-warning-subtle text-warning">
                                    <i class="bi bi-exclamation-circle me-1"></i>Not Paid
                                </span>
                            @endif
                        </div>
                    </div>
                    <div class="col-md-6">
                        <label class="form-label fw-medium text-muted">Profile Visibility</label>
                        <div>
                            @if($user->is_public_profile ?? true)
                                <span class="badge bg-info-subtle text-info">
                                    <i class="bi bi-eye me-1"></i>Public
                                </span>
                            @else
                                <span class="badge bg-secondary-subtle text-secondary">
                                    <i class="bi bi-eye-slash me-1"></i>Private
                                </span>
                            @endif
                        </div>
                    </div>
                    <div class="col-md-6">
                        <label class="form-label fw-medium text-muted">Last Updated</label>
                        <div>{{ $user->updated_at->format('M d, Y H:i') }}</div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Image Modal -->
<div class="modal fade" id="imageModal" tabindex="-1" aria-hidden="true">
    <div class="modal-dialog modal-lg modal-dialog-centered">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Gallery Image</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body text-center">
                <img id="modalImage" src="" alt="Gallery Image" class="img-fluid rounded">
            </div>
        </div>
    </div>
</div>
@endsection

@push('scripts')
<script>
function showImageModal(imageSrc) {
    document.getElementById('modalImage').src = imageSrc;
    new bootstrap.Modal(document.getElementById('imageModal')).show();
}

// User action handlers
document.addEventListener('DOMContentLoaded', function() {
    // Suspend user
    document.querySelectorAll('.btn-suspend-user').forEach(button => {
        button.addEventListener('click', function() {
            const userId = this.dataset.userId;
            if (confirm('Are you sure you want to suspend this user?')) {
                // Add your suspend user logic here
                console.log('Suspending user:', userId);
            }
        });
    });

    // Activate user
    document.querySelectorAll('.btn-activate-user').forEach(button => {
        button.addEventListener('click', function() {
            const userId = this.dataset.userId;
            if (confirm('Are you sure you want to activate this user?')) {
                // Add your activate user logic here
                console.log('Activating user:', userId);
            }
        });
    });
});
</script>
@endpush
